"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { Phone, Mail, MapPin, Clock, Send, Facebook, Instagram, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

export function AboutContactSection() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    company: "",
    service: "",
    message: ""
  });

  const contactInfo = [
    {
      icon: Phone,
      title: "Hotline",
      content: "0796 59 78 78",
      description: "Hỗ trợ 24/7, tư vấn miễn phí",
      color: "green",
      action: "tel:0796597878"
    },
    {
      icon: Mail,
      title: "Email",
      content: "<EMAIL>",
      description: "Phản hồi trong vòng 2 giờ",
      color: "blue",
      action: "mailto:<EMAIL>"
    },
    {
      icon: MapPin,
      title: "Địa chỉ",
      content: "T<PERSON><PERSON> <PERSON><PERSON>",
      description: "<PERSON><PERSON><PERSON>ă<PERSON> showroom c<PERSON><PERSON> chúng tôi",
      color: "purple",
      action: "#"
    },
    {
      icon: Clock,
      title: "Giờ làm việc",
      content: "8:00 - 18:00",
      description: "Thứ 2 - Chủ nhật hàng tuần",
      color: "orange",
      action: "#"
    }
  ];

  const socialLinks = [
    {
      icon: Facebook,
      name: "Facebook",
      url: "#",
      color: "blue",
      followers: "10K+"
    },
    {
      icon: Instagram,
      name: "Instagram", 
      url: "#",
      color: "pink",
      followers: "5K+"
    },
    {
      icon: MessageCircle,
      name: "Zalo",
      url: "#",
      color: "blue",
      followers: "Chat ngay"
    }
  ];

  const services = [
    "May gia công số lượng ít",
    "Thiết kế thời trang",
    "Tư vấn thương hiệu",
    "In thêu logo",
    "Chụp ảnh sản phẩm",
    "Dịch vụ khác"
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({
      name: "",
      email: "",
      phone: "",
      company: "",
      service: "",
      message: ""
    });
  };

  return (
    <section id="lien-he" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            LIÊN HỆ & ĐẶT MAY
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Mọi quan tâm của khách hàng với xưởng may NS Shop đều sẽ được phản hồi trong thời gian sớm nhất.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Gửi yêu cầu tư vấn
            </h3>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Họ và tên *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors"
                    placeholder="Nhập họ tên của bạn"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số điện thoại *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors"
                    placeholder="Nhập số điện thoại"
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors"
                    placeholder="Nhập email của bạn"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tên công ty/shop
                  </label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors"
                    placeholder="Tên công ty hoặc shop"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dịch vụ quan tâm
                </label>
                <select
                  name="service"
                  value={formData.service}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors"
                >
                  <option value="">Chọn dịch vụ</option>
                  {services.map((service, index) => (
                    <option key={index} value={service}>
                      {service}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nội dung tin nhắn *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={5}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-100 transition-colors resize-none"
                  placeholder="Mô tả chi tiết yêu cầu của bạn..."
                ></textarea>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300"
              >
                <Send className="mr-2 h-5 w-5" />
                Gửi yêu cầu tư vấn
              </Button>
            </form>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Contact Methods */}
            <div className="grid gap-6">
              {contactInfo.map((info, index) => {
                const Icon = info.icon;
                return (
                  <motion.a
                    key={index}
                    href={info.action}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-4 p-6 bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 group"
                  >
                    <div className={`flex-shrink-0 w-14 h-14 bg-${info.color}-100 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`h-7 w-7 text-${info.color}-600`} />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-gray-900 group-hover:text-pink-600 transition-colors">
                        {info.title}
                      </h4>
                      <p className="text-xl font-semibold text-gray-800">
                        {info.content}
                      </p>
                      <p className="text-sm text-gray-500">
                        {info.description}
                      </p>
                    </div>
                  </motion.a>
                );
              })}
            </div>

            {/* Social Media */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            >
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Kết nối với chúng tôi
              </h4>
              
              <div className="space-y-4">
                {socialLinks.map((social, index) => {
                  const Icon = social.icon;
                  return (
                    <a
                      key={index}
                      href={social.url}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 group"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 bg-${social.color}-100 rounded-full flex items-center justify-center`}>
                          <Icon className={`h-5 w-5 text-${social.color}-600`} />
                        </div>
                        <span className="font-medium text-gray-900 group-hover:text-pink-600 transition-colors">
                          {social.name}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {social.followers}
                      </span>
                    </a>
                  );
                })}
              </div>
            </motion.div>

            {/* Map Placeholder */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            >
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Vị trí showroom
              </h4>
              
              <div className="aspect-video bg-gray-100 rounded-xl flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Bản đồ showroom</p>
                  <p className="text-sm text-gray-400">TP. Hồ Chí Minh</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-pink-500 to-purple-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">
              Sẵn sàng bắt đầu dự án của bạn?
            </h3>
            <p className="text-pink-100 mb-8 max-w-2xl mx-auto">
              Đừng chần chừ! Liên hệ với NS Shop ngay hôm nay để được tư vấn miễn phí 
              và nhận báo giá tốt nhất cho dự án thời trang của bạn.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="bg-white text-pink-600 hover:bg-gray-100 px-8 py-3 rounded-full font-semibold"
              >
                <Phone className="mr-2 h-5 w-5" />
                Gọi ngay: 0796 59 78 78
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-pink-600 px-8 py-3 rounded-full font-semibold"
              >
                <MessageCircle className="mr-2 h-5 w-5" />
                Chat qua Zalo
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
